# LX-Chatbot 폐쇄망 환경 설치 가이드

## 1. 온라인 환경에서 패키지 준비

```bash
# 프로젝트 루트에서 실행
cd docker

# 애플리케이션 빌드
docker build -t lx-chatbot/frontend:latest -f Dockerfile ../..

# 이미지 저장
docker save lx-chatbot/frontend:latest -o lx-chatbot-frontend.tar
docker save node:20-alpine -o node-20-alpine.tar
```

또는 `prepare-offline.sh` 스크립트를 실행하면 위 명령어들이 자동으로 수행됩니다.

## 2. 폐쇄망으로 파일 복사

다음 파일들을 폐쇄망 환경으로 복사:
- `lx-chatbot-frontend.tar`
- `node-20-alpine.tar`
- `docker-compose.yml`
- `.env` (환경변수 설정)

## 3. 폐쇄망에서 수동 설치

### 3.1 환경변수 설정

`.env` 파일 생성:
```bash
cat > .env << EOF
DIFY_APP_KEY=your_dify_app_key
DIFY_URL=https://your-internal-dify-server/v1
VLLM_BASE_URL=http://your-internal-vllm-server:8005/v1
VLLM_API_KEY=your_vllm_api_key
EOF
```

### 3.2 Docker 이미지 로드

```bash
# 이미지 로드
docker load -i lx-chatbot-frontend.tar
docker load -i node-20-alpine.tar

# 이미지 확인
docker images | grep -E "(lx-chatbot|node)"
```

### 3.3 애플리케이션 실행

```bash
# 컨테이너 실행
docker-compose up -d

# 상태 확인
docker-compose ps

# 로그 확인
docker-compose logs -f
```

### 3.4 접속 확인

브라우저에서 `http://localhost:3002` 접속

## 4. 관리 명령어

```bash
# 서비스 중지
docker-compose down

# 서비스 재시작
docker-compose restart

# 로그 실시간 확인
docker-compose logs -f geon-ai

# 컨테이너 내부 접속
docker-compose exec geon-ai sh
```

## 5. Windows 환경

### PowerShell에서 실행:
```powershell
# 이미지 로드
docker load -i lx-chatbot-frontend.tar
docker load -i node-20-alpine.tar

# 환경변수 파일 생성
@"
DIFY_APP_KEY=your_dify_app_key
DIFY_URL=https://your-internal-dify-server/v1
VLLM_BASE_URL=http://your-internal-vllm-server:8005/v1
VLLM_API_KEY=your_vllm_api_key
NODE_ENV=production
"@ | Out-File -FilePath .env -Encoding UTF8

# 실행
docker-compose up -d
```
