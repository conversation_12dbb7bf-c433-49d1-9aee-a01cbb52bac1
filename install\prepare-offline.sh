#!/bin/bash

# 폐쇄망 설치를 위한 이미지 준비 스크립트
echo "=== 폐쇄망 설치용 이미지 준비 스크립트 ==="

# 현재 디렉토리 확인
if [ ! -f "../package.json" ]; then
    echo "❌ 프로젝트 루트에서 실행해주세요."
    echo "사용법: cd /path/to/lx-chatbot && ./docker/prepare-offline.sh"
    exit 1
fi

# 1. 애플리케이션 빌드 (프로젝트 루트에서)
echo "1. 애플리케이션 빌드 중..."
cd ..
docker build -t lx-chatbot/frontend:latest -f Dockerfile .
cd docker

# 2. 이미지 저장
echo "2. Docker 이미지 저장 중..."

# 메인 애플리케이션 이미지 저장
docker save lx-chatbot/frontend:latest -o lx-chatbot-frontend.tar
echo "✓ lx-chatbot-frontend.tar 저장 완료"

# 베이스 이미지 저장
docker save node:20-alpine -o node-20-alpine.tar
echo "✓ node-20-alpine.tar 저장 완료"

# 3. 파일 크기 확인
echo "3. 저장된 파일 정보:"
ls -lh *.tar

# 4. 필요한 파일들 복사
echo "4. 설치 파일들 준비 중..."
chmod +x install-offline.sh

# 5. 압축 (선택사항)
echo "5. 파일 압축 중..."
tar -czf lx-chatbot-offline-package.tar.gz *.tar docker-compose.yml .env.example install-offline.sh install-offline.bat
echo "✓ lx-chatbot-offline-package.tar.gz 생성 완료"

echo ""
echo "=== 준비 완료 ==="
echo "폐쇄망으로 복사할 파일들:"
echo "- lx-chatbot-offline-package.tar.gz (전체 패키지)"
echo "또는 개별 파일들:"
echo "- lx-chatbot-frontend.tar"
echo "- node-20-alpine.tar"
echo "- docker-compose.yml"
echo "- install-offline.sh (Linux용)"
echo "- install-offline.bat (Windows용)"
echo "- .env (환경변수 설정)"
