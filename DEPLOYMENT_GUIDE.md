# LX Chatbot 폐쇄망 배포 가이드

## 개요

이 문서는 LX Chatbot을 폐쇄망 환경에 배포하는 전체 과정을 설명합니다.

## 전체 흐름

```mermaid
graph TD
    A[온라인 환경] --> B[애플리케이션 빌드]
    B --> C[Docker 이미지 생성]
    C --> D[이미지 tar 파일로 저장]
    D --> E[설치 패키지 생성]
    E --> F[폐쇄망으로 파일 복사]
    F --> G[폐쇄망에서 이미지 로드]
    G --> H[환경변수 설정]
    H --> I[컨테이너 실행]
    I --> J[서비스 접속 확인]
```

## 1단계: 온라인 환경에서 준비

### 1.1 이미지 빌드

```bash
# 프로젝트 루트에서 실행
docker build -t lx-chatbot/frontend:latest .
```

### 1.2 이미지 저장

```bash
# docker 디렉토리로 이동
cd docker

# 이미지를 tar 파일로 저장
docker save lx-chatbot/frontend:latest -o lx-chatbot-frontend.tar
docker save node:20-alpine -o node-20-alpine.tar
```

### 1.3 자동화 스크립트 사용 (선택사항)

**Linux/macOS:**
```bash
./docker/prepare-offline.sh
```

**Windows:**
```powershell
powershell -ExecutionPolicy Bypass -File docker/prepare-offline.ps1
```

## 2단계: 폐쇄망으로 파일 전송

다음 파일들을 폐쇄망 환경으로 복사:

### 필수 파일
- `lx-chatbot-frontend.tar` (약 189MB)
- `node-20-alpine.tar` (약 137MB)
- `docker-compose.yml`
- `.env.example`

### 설치 스크립트 (선택사항)
- `install-offline.sh` (Linux/macOS용)
- `install-offline.bat` (Windows용)

## 3단계: 폐쇄망에서 설치

### 3.1 자동 설치 (권장)

**Linux/macOS:**
```bash
chmod +x install-offline.sh
./install-offline.sh
```

**Windows:**
```cmd
install-offline.bat
```

### 3.2 수동 설치

#### 3.2.1 Docker 이미지 로드
```bash
docker load -i lx-chatbot-frontend.tar
docker load -i node-20-alpine.tar
```

#### 3.2.2 환경변수 설정
```bash
# .env 파일 생성
cat > .env << EOF
DIFY_APP_KEY=your_dify_app_key_here
DIFY_URL=http://your-dify-server:port
EOF
```

#### 3.2.3 컨테이너 실행
```bash
docker-compose up -d
```

## 4단계: 설치 확인

### 4.1 컨테이너 상태 확인
```bash
docker-compose ps
```

### 4.2 로그 확인
```bash
docker-compose logs -f
```

### 4.3 웹 접속 확인
브라우저에서 `http://localhost:3002` 접속

## 5단계: 관리 및 운영

### 일반적인 관리 명령어
```bash
# 서비스 중지
docker-compose down

# 서비스 재시작
docker-compose restart

# 실시간 로그 확인
docker-compose logs -f

# 컨테이너 내부 접속
docker-compose exec frontend sh
```

## 문제 해결

### 빌드 관련 문제
1. **`.npmrc` 파일 없음**: 프로젝트 루트에 `.npmrc` 파일이 있는지 확인
2. **빌드 컨텍스트 오류**: 프로젝트 루트에서 빌드 실행
3. **의존성 설치 실패**: 네트워크 연결 및 레지스트리 설정 확인

### 실행 관련 문제
1. **포트 충돌**: 3002 포트가 사용 중인지 확인
2. **환경변수 오류**: `.env` 파일의 값들이 올바른지 확인
3. **컨테이너 시작 실패**: `docker-compose logs` 로 오류 메시지 확인

### 네트워크 관련 문제
1. **외부 서비스 연결 실패**: DIFY_URL, VLLM_BASE_URL 확인
2. **API 키 오류**: DIFY_APP_KEY, VLLM_API_KEY 확인

## 보안 고려사항

1. **환경변수 보안**: `.env` 파일의 API 키들을 안전하게 관리
2. **네트워크 격리**: 필요한 포트만 외부에 노출
3. **컨테이너 보안**: 정기적인 베이스 이미지 업데이트

## 성능 최적화

1. **리소스 제한**: docker-compose.yml에서 메모리/CPU 제한 설정
2. **로그 관리**: 로그 로테이션 설정
3. **모니터링**: 컨테이너 상태 모니터링 도구 활용

## 백업 및 복구

1. **설정 백업**: `.env` 파일 및 docker-compose.yml 백업
2. **데이터 백업**: 필요시 볼륨 데이터 백업
3. **이미지 백업**: tar 파일들을 안전한 위치에 보관
