import { motion } from 'framer-motion';
import { BotIcon, CodeIcon, BuildingIcon } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface OverviewProps {
  selectedModelId?: string;
}

// LX 공유재산 챗봇 설정 정의
const chatbotConfig = {
  icon: BuildingIcon,
  title: 'LX 공유재산 챗봇 도우미',
  description: 'LX 공유재산 관련 업무를 도와드립니다!',
  accordionTitle: '지원 업무 범위',
  sections: [
    { title: "공유재산 관리" },
    { title: "업무 절차 안내" },
    { title: "법령 및 규정" },
    { title: "시스템 사용법" }
  ],
  footerText: 'LX 공유재산 업무에 대한 전문적인 도움을 제공해드립니다.'
};

export const Overview = ({ selectedModelId }: OverviewProps) => {
  // LX 공유재산 챗봇 설정 사용
  const config = chatbotConfig;
  const IconComponent = config.icon;

  return (
    <motion.div
      key="lx-chatbot-overview"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3 }}
      className="w-full max-w-sm backdrop-blur-sm mx-auto bg-gradient-to-b from-background/10 to-background/80 rounded-xl"
    >
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="p-6 space-y-6">
          <div className="relative">
            <motion.div
              className="flex items-center justify-center gap-4"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 150 }}
            >
              <IconComponent size={28} className="text-primary" />
              <span className="font-bold text-2xl">+</span>
              <BotIcon size={28} className="text-primary" />
            </motion.div>
            <motion.div
              className="text-center mt-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <h2 className="text-lg font-semibold bg-clip-text bg-gradient-to-r from-primary to-primary/80">
                {config.title}
              </h2>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <p className="text-sm text-center">
              {config.description}
            </p>

            <Accordion type="single" collapsible defaultValue="examples" className="flex justify-center bg-background/40 rounded-lg">
              <AccordionItem value="examples" className="border-none">
                <AccordionTrigger className="justify-center gap-2 py-3 hover:no-underline">
                  <span className="text-sm font-medium">{config.accordionTitle}</span>
                </AccordionTrigger>
                <AccordionContent className="py-4">
                  <motion.div
                    className="space-y-6"
                    variants={{
                      hidden: { opacity: 0 },
                      show: {
                        opacity: 1,
                        transition: { staggerChildren: 0.1 }
                      }
                    }}
                    initial="hidden"
                    animate="show"
                  >
                    {config.sections.map((section, idx) => (
                      <motion.div
                        key={idx}
                        variants={{
                          hidden: { opacity: 0, y: 10 },
                          show: { opacity: 1, y: 0 }
                        }}
                        className="space-y-2"
                      >
                        <h3 className="text-sm font-medium text-primary">{section.title}</h3>
                      </motion.div>
                    ))}
                  </motion.div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </motion.div>

          <motion.p
            className="text-xs text-center text-muted-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            {config.footerText}
          </motion.p>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default Overview;