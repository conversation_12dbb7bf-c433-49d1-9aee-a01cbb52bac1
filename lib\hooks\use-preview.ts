import { useCallback, useMemo } from 'react';
import useS<PERSON> from 'swr';

const initialPreviewData: PreviewBlock = {
  documentId: 'init',
  content: '',
  kind: 'text',
  title: '',
  status: 'idle',
  isVisible: false,
  boundingBox: {
    top: 0,
    left: 0,
    width: 0,
    height: 0,
  },
};

export interface PreviewBlock {
  documentId: string;
  content: string;
  kind: string;
  title: string;
  status: 'idle' | 'streaming';
  isVisible: boolean;
  boundingBox: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
}


export function usePreview() {
  const { data: localPreview, mutate: setLocalPreview } = useSWR<PreviewBlock>(
    'preview',
    null,
    {
      fallbackData: initialPreviewData,
    }
  );

  const preview = useMemo(() => {
    if (!localPreview) return initialPreviewData;
    return localPreview;
  }, [localPreview]);

  const setPreview = useCallback(
    (updaterFn: PreviewBlock | ((currentPreview: PreviewBlock) => PreviewBlock)) => {
      setLocalPreview((currentPreview) => {
        const previewToUpdate = currentPreview || initialPreviewData;

        if (typeof updaterFn === 'function') {
          return updaterFn(previewToUpdate);
        }

        return updaterFn;
      });
    },
    [setLocalPreview]
  );

  return useMemo(() => ({ preview, setPreview }), [preview, setPreview]);
}