'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ChatRequestOptions, CreateMessage, Message } from 'ai';
import { memo } from 'react';

interface SuggestedActionsProps {
  chatId: string;
  append: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  selectedModelId?: string;
}

// LX 공유재산 챗봇 제안 액션 정의
const lxChatbotSuggestedActions = [
  {
    title: '공유재산 관리',
    label: '공유재산 관리 업무는 어떻게 하나요?',
    action: '공유재산 관리 업무는 어떻게 하나요?',
  },
  {
    title: '업무 절차',
    label: '공유재산 관련 업무 절차를 알려주세요',
    action: '공유재산 관련 업무 절차를 알려주세요',
  },
  {
    title: '법령 및 규정',
    label: '공유재산 관련 법령과 규정은 무엇인가요?',
    action: '공유재산 관련 법령과 규정은 무엇인가요?',
  },
  {
    title: '시스템 사용법',
    label: '공유재산 시스템 사용법을 알려주세요',
    action: '공유재산 시스템 사용법을 알려주세요',
  },
];

function PureSuggestedActions({ chatId, append, selectedModelId }: SuggestedActionsProps) {
  // LX 공유재산 챗봇 제안 액션 사용
  const suggestedActions = lxChatbotSuggestedActions;

  return (
    <div className="grid sm:grid-cols-2 gap-2 w-full">
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ delay: 0.05 * index }}
          key={`suggested-action-${suggestedAction.title}-${index}`}
          className="block"
        >
          <Button
            variant="ghost"
            onClick={async () => {
              // window.history.replaceState({}, '', `/chat/${chatId}`);

              append({
                role: 'user',
                content: suggestedAction.action,
              });
            }}
            className="text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start"
          >
            <span className="font-medium">{suggestedAction.title}</span>
            <span className="text-muted-foreground">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(PureSuggestedActions, () => true);
