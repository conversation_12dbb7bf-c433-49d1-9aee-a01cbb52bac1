/**
 * 전문적인 디자인 토큰 시스템
 * 일관성 있는 UI/UX를 위한 디자인 시스템
 */

export const designTokens = {
  // 색상 시스템 - 전문적이고 세련된 팔레트
  colors: {
    // Primary - 신뢰감 있는 블루 계열
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe', 
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9', // 메인 컬러
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
    },
    
    // Success - 자연스러운 그린
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
    },
    
    // Warning - 따뜻한 오렌지
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
    },
    
    // Error - 부드러운 레드
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
    },
    
    // Neutral - 세련된 그레이 스케일
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
    },
  },
  
  // 타이포그래피 시스템
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'monospace'],
    },
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
  
  // 간격 시스템
  spacing: {
    xs: '0.25rem',   // 4px
    sm: '0.5rem',    // 8px
    md: '0.75rem',   // 12px
    lg: '1rem',      // 16px
    xl: '1.5rem',    // 24px
    '2xl': '2rem',   // 32px
    '3xl': '3rem',   // 48px
  },
  
  // 반지름 시스템
  borderRadius: {
    sm: '0.375rem',  // 6px
    md: '0.5rem',    // 8px
    lg: '0.75rem',   // 12px
    xl: '1rem',      // 16px
    '2xl': '1.5rem', // 24px
  },
  
  // 그림자 시스템 - 깊이감 있는 전문적 그림자
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  },
  
  // 애니메이션 시스템
  animation: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
    },
    easing: {
      ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
} as const;

// 컴포넌트별 스타일 프리셋
export const componentStyles = {
  // 카드 스타일
  card: {
    base: `
      bg-white/80 backdrop-blur-sm border border-neutral-200/60 
      rounded-xl shadow-md hover:shadow-lg transition-all duration-200
    `,
    interactive: `
      hover:border-neutral-300/80 hover:-translate-y-0.5 
      cursor-pointer active:scale-[0.98]
    `,
    success: `
      bg-gradient-to-br from-success-50/90 to-success-100/70 
      border-success-200/60 hover:border-success-300/80
    `,
    warning: `
      bg-gradient-to-br from-warning-50/90 to-warning-100/70 
      border-warning-200/60 hover:border-warning-300/80
    `,
    error: `
      bg-gradient-to-br from-error-50/90 to-error-100/70 
      border-error-200/60 hover:border-error-300/80
    `,
  },
  
  // 버튼 스타일
  button: {
    primary: `
      bg-primary-500 hover:bg-primary-600 text-white 
      shadow-md hover:shadow-lg active:scale-95
      transition-all duration-200 font-medium
    `,
    secondary: `
      bg-neutral-100 hover:bg-neutral-200 text-neutral-700 
      border border-neutral-300 hover:border-neutral-400
      transition-all duration-200 font-medium
    `,
    ghost: `
      bg-transparent hover:bg-neutral-100 text-neutral-600 hover:text-neutral-800
      transition-all duration-200 font-medium
    `,
  },
  
  // 배지 스타일
  badge: {
    success: `
      bg-success-100 text-success-700 border border-success-200
      font-medium text-xs px-2 py-1 rounded-md
    `,
    warning: `
      bg-warning-100 text-warning-700 border border-warning-200
      font-medium text-xs px-2 py-1 rounded-md
    `,
    info: `
      bg-primary-100 text-primary-700 border border-primary-200
      font-medium text-xs px-2 py-1 rounded-md
    `,
    neutral: `
      bg-neutral-100 text-neutral-700 border border-neutral-200
      font-medium text-xs px-2 py-1 rounded-md
    `,
  },
  
  // 아이콘 컨테이너
  iconContainer: {
    sm: `
      flex h-6 w-6 items-center justify-center rounded-full
      bg-neutral-100 text-neutral-600
    `,
    md: `
      flex h-8 w-8 items-center justify-center rounded-full
      bg-neutral-100 text-neutral-600
    `,
    lg: `
      flex h-10 w-10 items-center justify-center rounded-full
      bg-neutral-100 text-neutral-600
    `,
  },
} as const;

// 유틸리티 함수들
export const getColorClass = (color: keyof typeof designTokens.colors, shade: number = 500) => {
  return `${color}-${shade}`;
};

export const getSpacingClass = (size: keyof typeof designTokens.spacing) => {
  return designTokens.spacing[size];
};
