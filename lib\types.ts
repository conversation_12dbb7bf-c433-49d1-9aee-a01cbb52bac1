import { CoreMessage } from 'ai'

export type Message = CoreMessage & {
  id: string
}

export interface Chat extends Record<string, any> {
  id: string
  title: string
  createdAt: Date
  userId: string
  path: string
  messages: Message[]
  sharePath?: string
}

export type ServerActionResult<Result> = Promise<
  | Result
  | {
      error: string
    }
>

export interface AuthResult {
  type: string
  message: string
}

export interface User extends Record<string, any> {
  id: string
  email: string
  password: string
  salt: string
}

export interface ToolResult {
  response: any;
}

export type LayerStyle = {
  rules: Array<{
    name: string;
    symbolizers: Array<{
      kind: string;
      color: string;
      wellKnownName: string;
      fillOpacity: number;
      radius: number;
      strokeColor: string;
      strokeOpacity: number;
      strokeWidth: number;
    }>
  }>;
  name: string;
};

export type ContentLayerInfo = {
  Layer: boolean;
};

export type ContentItem = {
  mapUrl: string | null;
  lyrGroupSeCode: string;
  title: string;
  layerNcm: string;
  contentId: string;
  odfLayerId: string;
  cntntsId: string;
  upperGroupId: string;
  linkedLayer: ContentLayerInfo;
  layerId: string;
  onOffAt: string;
  style: {
    isTypeStyleFieldLimit: boolean;
    geometryType: string;
    labelFlag: boolean;
    targetLayer: ContentLayerInfo;
    targetLayerService: string;
    originStyleObject: {
      styleObject: LayerStyle;
      opacity: number;
    };
    nowStyleObject: {
      styleObject: LayerStyle;
      opacity: number;
    };
    previewStyleObject: {
      option: {
        useManualEditing: boolean;
      };
      styleObject: LayerStyle;
      opacity: number;
    };
    targetLayerId: string;
    useMultiStyle: boolean;
  };
  attributes: any[];
  jobClCode: string;
  lyrClCode: string;
  lyrClSeCode: string;
  svcTySeCode: string;
  popup: {
    layerInfo: {
      layerObject: ContentLayerInfo;
      layerId: string;
      layerNm: string;
      layerCallType: string;
    };
  };
  lyrTySeCode: string;
  toc: {
    popupSet: boolean;
    setGroupName: boolean;
    setLayerNcm: boolean;
    delete: boolean;
    attributeGrid: boolean;
    legend: boolean;
    icon: boolean;
    setVisible: boolean;
    setLabel: boolean;
    layerDetail: boolean;
    attributePopup: boolean;
    styleSet: boolean;
    labelStyleSet: boolean;
    webLayerUpdatePopup: boolean;
    visibleRange: boolean;
  };
  isPopupVisible: boolean;
  isLegendVisible: boolean;
  lyrGroupLevelCode: string;
  lyrGroupNm: string;
  lyrSortOrdr: number;
};
