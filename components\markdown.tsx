import Link from 'next/link';
import React, { memo } from 'react';
import ReactMarkdown, { type Components } from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { CodeBlock } from './code-block';

const components: Partial<Components> = {
  code: CodeBlock,
  p: ({ node, children, ...props }) => {
    return (
      <p className="px-2" {...props}>
        {children}
      </p>
    );
  },
  ol: ({ node, children, ...props }) => {
  return (
    <ol
      className="space-y-1.5 pl-0 counter-reset-[custom-counter] text-sm leading-relaxed"
      style={{ counterReset: 'custom-counter' }}
      {...props}
    >
      {children}
    </ol>
  );
},
li: ({ node, children, ...props }) => {
  return (
    <li className="relative pl-4  text-gray-800 before:content-['•'] before:absolute before:left-0 before:text-gray-400" {...props}>
      {children}
    </li>
  );
},
  ul: ({ node, children, ...props }) => {
    return (
      <ul className=" list-outside ml-4" {...props}>
        {children}
      </ul>
    );
  },
  strong: ({ node, children, ...props }) => {
    return (
      <span className="font-semibold" {...props}>
        {children}
      </span>
    );
  },
  a: ({ node, children, ...props }) => {
    return (
      <Link
        className="text-blue-500 hover:underline"
        target="_blank"
        rel="noreferrer"
        {...props}
      >
        {children}
      </Link>
    );
  },
  h1: ({ node, children, ...props }) => {
    return (
      <h1 className="text-3xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h1>
    );
  },
  h2: ({ node, children, ...props }) => {
    return (
      <h2 className="text-2xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h2>
    );
  },
  h3: ({ node, children, ...props }) => {
    return (
      <h3 className="text-xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h3>
    );
  },
  h4: ({ node, children, ...props }) => {
    return (
      <h4 className="text-lg font-semibold mt-6 mb-2" {...props}>
        {children}
      </h4>
    );
  },
  h5: ({ node, children, ...props }) => {
    return (
      <h5 className="text-base font-semibold mt-6 mb-2" {...props}>
        {children}
      </h5>
    );
  },
  h6: ({ node, children, ...props }) => {
    return (
      <h6 className="text-sm font-semibold mt-6 mb-2" {...props}>
        {children}
      </h6>
    );
  },
  table: ({ node, children, ...props }) => {
    return (
      <table className="min-w-full border-collapse border my-4" {...props}>
        {children}
      </table>
    );
  },
  thead: ({ node, children, ...props }) => {
    return (
      <thead {...props}>
        {children}
      </thead>
    );
  },
  tr: ({ node, children, ...props }) => {
    return (
      <tr className="border-b " {...props}>
        {children}
      </tr>
    );
  },
  th: ({ node, children, ...props }) => {
    return (
      <th className="px-6 py-3 text-left text-sm font-semibold  border-r  last:border-r-0" {...props}>
        {children}
      </th>
    );
  },
  td: ({ node, children, ...props }) => {
    return (
      <td className="px-6 py-4 text-sm border-r last:border-r-0" {...props}>
        {children}
      </td>
    );
  },
};

const remarkPlugins = [remarkGfm];

const NonMemoizedMarkdown = ({ children }: { children: string }) => {
  return (
    <ReactMarkdown remarkPlugins={remarkPlugins} components={components}>
      {children}
    </ReactMarkdown>
  );
};

export const Markdown = memo(
  NonMemoizedMarkdown,
  (prevProps, nextProps) => prevProps.children === nextProps.children,
);
