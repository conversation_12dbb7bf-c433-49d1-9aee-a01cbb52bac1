import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: '업무지원(챗봇)',
    short_name: 'GeOn',
    description: 'AI 기반 지도 서비스 - 말로 만드는 지도와 지능형 어시스턴트',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#000000',
    icons: [
      {
        src: '/favicon.ico',
        sizes: 'any',
        type: 'image/x-icon',
      },
      {
        src: '/images/main-logo.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        src: '/images/main-logo.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
    categories: ['productivity', 'utilities', 'navigation'],
    lang: 'ko',
    orientation: 'portrait-primary',
  }
}
