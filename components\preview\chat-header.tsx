'use client';

import { useRouter } from 'next/navigation';
import { useWindowSize } from 'usehooks-ts';

import { ModelSelector } from '@/components/model-selector';
// import { SidebarToggle } from '@/components/sidebar-toggle';
import { Button } from '@/components/ui/button';
// import { PlusIcon, VercelIcon } from './icons';
// import { useSidebar } from './ui/sidebar';
import { memo } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { PlusIcon } from 'lucide-react';
import { ThemeToggle } from '../theme-toggle';
// import { VisibilityType, VisibilitySelector } from './visibility-selector';

function PureChatHeader({
  chatId,
  selectedModelId,
  // selectedVisibilityType,
  isReadonly,
}: {
  chatId: string;
  selectedModelId: string;
  // selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
}) {
  const router = useRouter();
  // const { open } = useSidebar();

  const { width: windowWidth } = useWindowSize();

  return (
    <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2">
      {/* <SidebarToggle /> */}

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            className="order-2 md:order-2 md:px-2 px-2 md:h-fit ml-auto md:ml-0"
            onClick={() => {
              router.refresh();
            }}
          >
            <PlusIcon />
            <span className="md:sr-only">새 대화</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>새 대화</TooltipContent>
      </Tooltip>

      <ModelSelector
        selectedModelId={selectedModelId}
        className="order-1 md:order-1"
      />

      <ThemeToggle className="order-3 ml-auto" />

    </header>
  );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
  return prevProps.selectedModelId === nextProps.selectedModelId;
});
