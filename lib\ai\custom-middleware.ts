import { LanguageModelV1StreamPart, LanguageModelV1Middleware } from "ai";

export const customMiddleware: LanguageModelV1Middleware = {
  wrapStream: async ({ doStream }) => {
	const { stream, ...rest } = await doStream();

    const transformStream = new TransformStream<
      LanguageModelV1StreamPart,
      LanguageModelV1StreamPart
    >({
      transform(chunk, controller) {
        controller.enqueue(chunk);
      },

      flush() {
      },
    });

    return {
      stream: stream.pipeThrough(transformStream),
      ...rest,
    };
  },
};
