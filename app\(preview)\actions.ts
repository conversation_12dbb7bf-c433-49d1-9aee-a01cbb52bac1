'use server';


import { cookies } from 'next/headers';

export async function saveModelId(model: string) {
  const cookieStore = await cookies();
  cookieStore.set('model-id', model);
}

export async function saveDevModelId(model: string) {
  const cookieStore = await cookies();
  cookieStore.set('dev-model-id', model);
}

export async function generateTitleFromUserMessage({
  message,
}: {
  message: any;
}) {
  // Preview 모드에서는 기본 제목 반환
  return "Preview Chat";
}

export async function deleteTrailingMessages({ id }: { id: string }) {
  // Preview 모드에서는 아무것도 하지 않음
  console.log('deleteTrailingMessages called in preview mode - no action taken');
}

export async function updateChatVisibility({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: string;
}) {
  // Preview 모드에서는 아무것도 하지 않음
  console.log('updateChatVisibility called in preview mode - no action taken');
}


// export async function getChats(userId?: string | null) {
// 	const session = await auth()
// 	if (!userId) {
// 		return []
// 	}
// 	if (userId !== session?.user?.id) {
// 		return []
// 	}
// 	try {
// 		const chats: string[] = await redis.zrange(`user:chat:${userId}`, 0, -1, 'REV')
// 		const pipeline = redis.pipeline()
// 		for (const chat of chats) {
// 			pipeline.hgetall(chat)
// 		}
// 		const results = await pipeline.exec()
// 		return results?.map(result => result[1]) as Chat[]
// 	} catch (error) {
// 		return []
// 	}
// }

// export async function getChat(id: string, userId: string) {
// 	const session = await auth()
// 	if (userId !== session?.user?.id) {
// 		return {
// 			error: 'Unauthorized'
// 		}
// 	}
// 	const chat = await redis.hgetall(`chat:${id}`)
// 	if (!chat || (userId && chat.userId !== userId)) {
// 		return null
// 	}

// 	// messages 필드를 JSON으로 파싱
// 	if (chat.messages) {
// 		try {
// 			chat.messages = JSON.parse(chat.messages);
// 		} catch (error) {
// 			console.error('Failed to parse messages:', error);
// 			chat.messages = ""; // 파싱 실패시 빈 값으로 설정
// 		}
// 	} else {
// 		chat.messages = ""; // messages 필드가 없으면 빈 값으로 설정
// 	}

// 	return chat as Chat
// }

// export async function removeChat({ id, path }: { id: string; path: string }) {
// 	const session = await auth()
// 	if (!session) {
// 		return {
// 			error: 'Unauthorized'
// 		}
// 	}
// 	const uid = await redis.hget(`chat:${id}`, 'userId')
// 	if (uid !== session?.user?.id) {
// 		return {
// 			error: 'Unauthorized'
// 		}
// 	}
// 	await redis.del(`chat:${id}`)
// 	await redis.zrem(`user:chat:${session.user.id}`, `chat:${id}`)
// 	revalidatePath('/')
// 	return revalidatePath(path)
// }

// export async function clearChats() {
// 	const session = await auth()
// 	if (!session?.user?.id) {
// 		return {
// 			error: 'Unauthorized'
// 		}
// 	}
// 	const chats: string[] = await redis.zrange(`user:chat:${session.user.id}`, 0, -1)
// 	if (!chats.length) {
// 		return redirect('/')
// 	}
// 	const pipeline = redis.pipeline()
// 	for (const chat of chats) {
// 		pipeline.del(chat)
// 		pipeline.zrem(`user:chat:${session.user.id}`, chat)
// 	}
// 	await pipeline.exec()
// 	revalidatePath('/')
// 	return redirect('/')
// }

// export async function getSharedChat(id: string) {
// 	const chat = await redis.hgetall(`chat:${id}`)

// 	if (!chat || !chat.sharePath) {
// 		return null
// 	}

// 	// messages 필드를 JSON으로 파싱
// 	if (chat.messages) {
// 		try {
// 			chat.messages = JSON.parse(chat.messages);
// 		} catch (error) {
// 			console.error('Failed to parse messages:', error);
// 			chat.messages = ""; // 파싱 실패시 빈 값으로 설정
// 		}
// 	} else {
// 		chat.messages = ""; // messages 필드가 없으면 빈 값으로 설정
// 	}

// 	return chat as Chat
// }

// export async function shareChat(id: string) {
// 	const session = await auth()

// 	if (!session?.user?.id) {
// 		return {
// 			error: 'Unauthorized'
// 		}
// 	}

// 	const chat = await redis.hgetall(`chat:${id}`)

// 	if (!chat || chat.userId !== session.user.id) {
// 		return {
// 			error: 'Something went wrong'
// 		}
// 	}

// 	const payload = {
// 		...chat,
// 		sharePath: `/share/${chat.id}`,
// 	}

// 	await redis.hmset(`chat:${chat.id}`, payload)

// 	return payload
// }
// export async function saveChat(chat: Chat) {
// 	const session = await auth()
// 	if (session && session.user) {
// 		const pipeline = redis.pipeline()

// 		// messages를 JSON 문자열로 변환
// 		const chatToSave = {
// 			...chat,
// 			messages: JSON.stringify(chat.messages)
// 		};

// 		// chat 객체의 모든 필드를 Redis hash로 저장
// 		pipeline.hmset(`chat:${chat.id}`, chatToSave)

// 		// 사용자의 채팅 목록에 이 채팅을 추가
// 		pipeline.zadd(`user:chat:${chat.userId}`, Date.now(), `chat:${chat.id}`)

// 		// 파이프라인 실행
// 		await pipeline.exec()
// 	} else {
// 		return
// 	}
// }

// export async function getMissingKeys() {
// 	const keysRequired = ['OPENAI_API_KEY']
// 	return keysRequired
// 		.map(key => (process.env[key] ? '' : key))
// 		.filter(key => key !== '')
// }
