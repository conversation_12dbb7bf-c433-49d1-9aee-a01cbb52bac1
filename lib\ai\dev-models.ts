// Define your models here.

export interface Model {
  id: string;
  label: string;
  apiIdentifier: string; // Dify Application ID
  description: string;
  apiKey?: string;
}

export const models: Array<Model> = [
  {
    id: '공유재산 관리 어시스턴트',
    label: '공유재산 관리 어시스턴트',
    apiIdentifier: 'shared-property-assistant', // 공유재산 관련 Application ID
    description: '공유재산 관리를 위한 전문 어시스턴트',
    apiKey: process.env.DIFY_APP_KEY || 'app-shared-property-default', // 환경변수 우선 사용
  },
] as const;

export const DEFAULT_MODEL_NAME: string = '공유재산 관리 어시스턴트';

// API 키를 모델 ID로 매핑하는 함수
export function getApiKeyByModelId(modelId: string): string | undefined {
  const model = models.find(m => m.id === modelId);
  return model?.apiKey;
}
