# Front-Chat

Next.js 15 기반의 AI 채팅 애플리케이션입니다. 말로 만드는 지도 기능과 개발자 지원센터를 제공합니다.

## 🚀 기술 스택

- **Framework:** Next.js 15 (App Router)
- **Runtime:** Node.js 20.18 LTS 이상
- **Package Manager:** pnpm
- **Database:** Drizzle ORM
- **Styling:** Tailwind CSS, shadcn/ui
- **Container:** Docker

## 🔧 시작하기

### 환경 설정

1. 프로젝트 클론
```bash
git clone [repository-url]
cd front-chat
```

2. 환경 변수 설정
- `.env.sample` 파일을 `.env.local`로 복사하여 필요한 환경 변수를 설정합니다.
```bash
cp .env.sample .env.local
```

### 개발 환경 실행

```bash
# 의존성 설치
pnpm install

# 개발 서버 실행
pnpm dev
```

### 데이터베이스 관리

DB 스키마 변경 시 다음 단계를 따릅니다:

1. `db/schema.ts` 파일을 수정합니다.
2. 마이그레이션 파일을 생성합니다:
```bash
pnpm drizzle-kit generate
```
3. 생성된 마이그레이션 파일은 `lib/drizzle` 폴더에서 확인할 수 있습니다.
4. DB에 변경사항을 적용합니다. 적용 전 .env 의 postgres 주소를 확인해주세요:
```bash
pnpm migrate
```

### Docker 환경

```bash
# 빌드
docker compose -f docker/geon-ai/docker-compose.yml build

# 실행
docker compose -f docker/geon-ai/docker-compose.yml up -d

# 중지
docker compose -f docker/geon-ai/docker-compose.yml down
```

## 📁 프로젝트 구조

```
front-chat/
├── ai/                    # AI 미들웨어 및 모델 정의
├── app/                   # 애플리케이션 Router
│   ├── (auth)/              # 인증 관련 기능
│   ├── (map)/               # 말로 만드는 지도 API 및 채팅
│   ├── (preview)/           # 개발자 지원센터 채팅
│   ├── api/                 # API 엔드포인트
│   └── providers.tsx        # 전역 상태 관리
├── components/            # 재사용 컴포넌트
├── docker/                # Docker 설정
├── db/                    # 데이터베이스 설정
├── lib/                   # 유틸리티 및 공통 로직
└── public/                # 정적 자원
```

## 📝 코딩 컨벤션

- **명명 규칙:** 케밥 케이스(kebab-case) 사용
- **상태 관리:** 전역 상태는 `app/providers.tsx`에서 관리
- **컴포넌트:** 재사용 가능한 컴포넌트는 `components/` 디렉토리에 위치

## 🤝 기여하기

1. 변경사항을 적용하기 전에 적절한 테스트를 진행해주세요.
2. DB 스키마 변경 시 마이그레이션 파일을 반드시 커밋에 포함해주세요.
3. 환경 변수 추가 시 `.env.sample` 파일을 업데이트해주세요.

## 📄 설정 파일

- `components.json`: shadcn/ui 설정
- `drizzle.config.ts`: Drizzle ORM 설정
- `next.config.mjs`: Next.js 설정
- `postcss.config.mjs`: PostCSS 설정
- `tailwind.config.ts`: Tailwind CSS 설정
- `tsconfig.json`: TypeScript 설정

## 📌 주의사항

- Node.js 20.18 이상 버전이 필요합니다.
- 환경 변수 설정 없이는 정상적인 실행이 불가능합니다.
- DB 스키마 변경 시 반드시 마이그레이션 절차를 따라주세요.