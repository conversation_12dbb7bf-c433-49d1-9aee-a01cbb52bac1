import { NextResponse } from "next/server";
// Preview 모드 - 인증 관련 import 주석처리
// import { auth } from "./app/(auth)/auth";

export const config = {
    matcher: [
        /*
         * Match all paths except for:
         * 1. /api routes
         * 2. /_next (Next.js internals)
         * 3. /_static (inside /public)
         * 4. all root files inside /public (e.g. /favicon.ico)
         */
        "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)",
    ],
};

// Preview 모드 - 인증 미들웨어 비활성화, 단순 리다이렉트만 처리
export default function middleware(req: any) {
    const url = req.nextUrl;
    const pathname = url.pathname;

    console.log(`[MIDDLEWARE] Preview mode - processing path: ${pathname}`);

    // 루트 경로 접근 시 preview로 리다이렉트
    if (pathname === '/') {
        console.log(`[MIDDLEWARE] Root path, redirecting to /preview`);
        return NextResponse.redirect(new URL('/preview', req.url));
    }

    // Preview 모드에서는 모든 경로 허용
    console.log(`[MIDDLEWARE] Preview mode - allowing all requests`);
    return NextResponse.next();
}
