# LX Chatbot Docker 배포 가이드

이 디렉토리는 LX Chatbot의 Docker 기반 배포를 위한 파일들을 포함합니다.

## 파일 구조

- `Dockerfile`: 애플리케이션 이미지 빌드용 (프로젝트 루트에 위치)
- `docker-compose.yml`: 서비스 오케스트레이션
- `prepare-offline.sh`: 폐쇄망 설치용 이미지 준비 스크립트
- `install-offline.sh`: Linux/macOS용 폐쇄망 설치 스크립트
- `install-offline.bat`: Windows용 폐쇄망 설치 스크립트
- `.env.example`: 환경변수 설정 예제

## 1. 일반 환경에서 빌드 및 실행

```bash
# 프로젝트 루트에서 이미지 빌드
docker build -t lx-chatbot/frontend:latest .

# docker 디렉토리에서 서비스 실행
cd docker
docker-compose up -d
```

## 2. 폐쇄망 환경 준비

### 2.1 자동 준비 (권장)

```bash
# 프로젝트 루트에서 실행
./install/prepare-offline.sh
```

이 스크립트는 다음을 수행합니다:
- 애플리케이션 이미지 빌드
- 필요한 베이스 이미지들을 tar 파일로 저장
- 폐쇄망 설치용 패키지 생성 (`lx-chatbot-offline-package.tar.gz`)

### 2.2 수동 준비

```bash
# 프로젝트 루트에서 이미지 빌드
docker build -t lx-chatbot/frontend:latest .

# docker 디렉토리로 이동
cd docker

# 이미지 저장
docker save lx-chatbot/frontend:latest -o lx-chatbot-frontend.tar
docker save node:20-alpine -o node-20-alpine.tar
```

## 3. 폐쇄망 환경에서 설치

### 3.1 자동 설치 (권장)

생성된 패키지를 폐쇄망 환경으로 복사하고 압축 해제한 후:

**Linux/macOS:**
```bash
chmod +x install-offline.sh
./install-offline.sh
```

**Windows:**
```cmd
install-offline.bat
```

### 3.2 수동 설치

#### 3.2.1 환경변수 설정

`.env` 파일 생성:
```bash
cat > .env << EOF
DIFY_APP_KEY=your_dify_app_key
DIFY_URL=http://your-internal-dify-server:port
VLLM_BASE_URL=http://your-internal-vllm-server:port/v1
VLLM_API_KEY=your_vllm_api_key
EOF
```

#### 3.2.2 Docker 이미지 로드

```bash
# 이미지 로드
docker load -i lx-chatbot-frontend.tar
docker load -i node-20-alpine.tar

# 이미지 확인
docker images | grep -E "(lx-chatbot|node)"
```

#### 3.2.3 애플리케이션 실행

```bash
# 컨테이너 실행
docker-compose up -d

# 상태 확인
docker-compose ps

# 로그 확인
docker-compose logs -f
```

### 3.4 접속 확인

브라우저에서 `http://localhost:3002` 접속

## 4. 환경변수 설정

`.env` 파일에서 다음 환경변수들을 설정해야 합니다:

- `DIFY_APP_KEY`: Dify 애플리케이션 키
- `DIFY_URL`: Dify 서버 URL (예: http://dify-server:80)
- `VLLM_BASE_URL`: vLLM 서버 URL (예: http://vllm-server:8000/v1)
- `VLLM_API_KEY`: vLLM API 키

## 5. 관리 명령어

```bash
# 서비스 중지
docker-compose down

# 서비스 재시작
docker-compose restart

# 로그 실시간 확인
docker-compose logs -f

# 컨테이너 내부 접속
docker-compose exec frontend sh
```

## 6. 문제 해결

### 빌드 오류

- `.npmrc` 파일이 프로젝트 루트에 있는지 확인
- 프로젝트 루트에서 빌드 명령 실행

### 컨테이너 실행 오류

- 환경변수가 올바르게 설정되었는지 확인
- 포트 3002가 사용 중이지 않은지 확인

### 로그 확인

```bash
docker-compose logs -f
```

## 7. 포트 및 접속

- 애플리케이션: `3002:3000` (호스트:컨테이너)
- 웹 브라우저에서 `http://localhost:3002`로 접속
