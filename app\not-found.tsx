import Link from 'next/link';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: '페이지를 찾을 수 없음',
  description: '요청하신 페이지를 찾을 수 없습니다. URL을 확인하거나 홈페이지로 돌아가세요.',
  robots: {
    index: false,
    follow: false,
  },
};

export default function NotFound() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background">
      <div className="mx-auto max-w-md text-center">
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-muted-foreground">404</h1>
          <h2 className="mt-4 text-2xl font-semibold">페이지를 찾을 수 없습니다</h2>
          <p className="mt-2 text-muted-foreground">
            요청하신 페이지가 존재하지 않거나 이동되었을 수 있습니다.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link
            href="/geon-2d-map"
            className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
          >
            홈으로 돌아가기
          </Link>
          
          <div className="text-sm text-muted-foreground">
            <p>또는</p>
            <Link
              href="/login"
              className="text-primary hover:underline"
            >
              로그인 페이지로 이동
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
