/* 
    Unfortunately in current drizzle-kit version we can't automatically get name for primary key.
    We are working on making it available!

    Meanwhile you can:
        1. Check pk name in your database, by running
            SELECT constraint_name FROM information_schema.table_constraints
            WHERE table_schema = 'public'
                AND table_name = 'map_view'
                AND constraint_type = 'PRIMARY KEY';
        2. Uncomment code below and paste pk name manually
        
    <PERSON> to release this update as soon as possible
*/

-- ALTER TABLE "map_view" DROP CONSTRAINT "<constraint_name>";--> statement-breakpoint
ALTER TABLE map_view DROP CONSTRAINT map_view_pkey;
ALTER TABLE "map_view" ADD CONSTRAINT "map_view_mapId_userId_pk" PRIMARY KEY("mapId","userId");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "map_view_id_idx" ON "map_view" USING btree ("id");